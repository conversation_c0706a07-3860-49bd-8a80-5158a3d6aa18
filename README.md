# TimerAnimB - 魔兽世界3.3.5登录动画插件

## 功能描述

TimerAnimB是一个专为魔兽世界3.3.5客户端设计的登录动画效果插件。它在玩家登录时自动播放光效渐变动画，使用官方的AnimationGroup和Alpha动画框架实现。

## 主要特性

- ✨ **自动触发**：玩家登录时自动播放动画
- ⏱️ **精确时序**：延迟2秒后开始，持续2.5秒的渐变动画
- 🎨 **双层贴图**：主贴图和光效贴图的完美结合
- 🔧 **3.3.5兼容**：完全兼容魔兽世界3.3.5客户端API
- 🛡️ **资源管理**：自动清理资源，防止内存泄漏
- 🐛 **调试友好**：详细的中文调试信息和测试命令

## 安装方法

1. 将整个`TimerAnimB`文件夹复制到魔兽世界安装目录下的`Interface\AddOns\`文件夹中
2. 启动游戏，在插件管理界面确保TimerAnimB已启用
3. 重新登录角色即可看到动画效果

## 使用说明

### 自动功能
- 插件加载后会自动创建动画框架
- 每次玩家登录时会自动触发动画播放
- 动画完成后会自动清理资源

### 手动测试命令
- `/timeranimb test` 或 `/tab test` - 立即播放测试动画
- `/timeranimb stop` 或 `/tab stop` - 停止当前播放的动画
- `/timeranimb status` 或 `/tab status` - 查看插件当前状态

## 技术实现

### 动画系统
- 使用`AnimationGroup`管理动画播放
- 使用`Alpha`动画实现透明度渐变
- 支持动画延迟、持续时间和缓动效果

### 贴图资源
- 主贴图：`Interface\Timer\challenges-logo.blp`
- 光效贴图：`Interface\Timer\challengesglow-logo.blp`
- 贴图尺寸：256x256像素，居中显示

### 事件处理
- `ADDON_LOADED` - 插件加载时创建框架
- `PLAYER_LOGIN` - 玩家登录时触发动画

## 配置参数

可以在代码中修改以下参数来自定义动画效果：

```lua
local ANIMATION_DELAY = 2.0        -- 登录后延迟时间（秒）
local ANIMATION_DURATION = 2.5     -- 动画持续时间（秒）
```

## 故障排除

### 常见问题

1. **动画不显示**
   - 检查贴图文件是否存在
   - 使用`/tab status`查看插件状态
   - 查看聊天窗口的调试信息

2. **动画重复播放**
   - 插件有防重复机制，正常情况下不会重复
   - 如果出现问题，使用`/tab stop`停止动画

3. **性能问题**
   - 动画使用官方API，性能开销极小
   - 动画完成后会自动清理资源

### 调试信息

插件会在聊天窗口输出详细的中文调试信息：
- 插件初始化状态
- 动画框架创建状态
- 动画播放进度
- 资源清理状态

## 开发信息

- **版本**：1.0.0
- **兼容性**：魔兽世界3.3.5客户端
- **开发语言**：Lua
- **API框架**：WoW Widget API

## 更新日志

### v1.0.0
- 初始版本发布
- 实现基础的登录动画功能
- 支持AnimationGroup和Alpha动画
- 添加调试命令和状态查询功能

## 许可证

本插件遵循开源许可证，可自由使用和修改。

---

**享受您的魔兽世界3.3.5动画体验！** ✨
