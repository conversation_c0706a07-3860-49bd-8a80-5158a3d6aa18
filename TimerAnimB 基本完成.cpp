-- TimerAnimB.lua
-- 魔兽世界3.3.5登录动画效果插件
-- 使用AnimationGroup和Alpha动画实现光效从不透明到透明的渐变效果
-- {{ AURA-X: 完全重写 - 使用3.3.5兼容的AnimationGroup和Alpha动画框架. Approval: 寸止(ID:1678886400). }}

-- 插件命名空间
TimerAnimB = {}

-- 动画配置参数
local ANIMATION_DELAY = 2        -- 登录后延迟2秒开始动画
local ANIMATION_DURATION = 0.2     -- 动画持续时间2.5秒

-- 贴图资源路径
local MAIN_TEXTURE = "Interface\\Timer\\challenges-logo"
local GLOW_TEXTURE = "Interface\\Timer\\challengesglow-logo"

-- 数字纹理样式配置表
TIMER_NUMBERS_SETS = {};
TIMER_NUMBERS_SETS["BigGold"]  = {
	texture = "Interface\\Timer\\bigtimernumbers",	-- 数字纹理文件路径
	w=256, h=170,									-- 单个数字的宽度和高度
	texW=1024, texH=512,							-- 纹理文件的总宽度和高度
	-- 每个数字的半宽度比例，用于精确定位和间距计算
	-- 数组索引对应数字0-9，值为相对于数字宽度的半宽比例
	numberHalfWidths = {
		--0,       1,       2,       3,       4,       5,       6,       7,       8,       9,
		35/128, 14/128, 33/128, 32/128, 36/128, 32/128, 33/128, 29/128, 31/128, 31/128,
	}
}

-- 全局变量
local animationFrame = nil
local mainTexAnimationGroup = nil
local glowTexAnimationGroup = nil
local Digit1AnimationGroup = nil
local Digit2AnimationGroup = nil
local Digit1GlowAnimationGroup = nil
local Digit2GlowAnimationGroup = nil
local isAnimationRunning = false

-- 创建主框架和动画系统
function TimerAnimB:CreateAnimationFrame()
    -- 创建主动画框架
    animationFrame = CreateFrame("Frame", "TimerAnimBMainFrame", UIParent)
    -- animationFrame:SetSize(256, 256)  -- 设置合适的尺寸
    -- animationFrame:SetPoint("CENTER", UIParent, "CENTER", 0, 0)  -- 居中显示
    -- animationFrame:SetFrameStrata("FULLSCREEN_DIALOG")  -- 设置为最高层级

    local timeSeconds = 20;
    animationFrame.time = timeSeconds;
    animationFrame.tentime = timeSeconds;
    animationFrame.emdtime = GetTime() + timeSeconds;
    animationFrame.style = TIMER_NUMBERS_SETS["BigGold"];

    TimerAnimB:CreateNumberAnimation()

    TimerAnimB:CreateGOAnimation()

    -- 初始隐藏框架
    animationFrame:Show()

    print("TimerAnimB: 动画框架和AnimationGroup创建完成")
end

-- 创建数字纹理和发光纹理以及动画
function TimerAnimB:CreateNumberAnimation()
    if not animationFrame then
        print("TimerAnimB: 错误 - 动画框架未创建")
        return
    end

    local digit1 = animationFrame:CreateTexture(nil, "OVERLAY")
    digit1:SetTexture(animationFrame.style.texture)
    -- digit1:SetSize(256, 256)
    digit1:SetSize(animationFrame.style.w/2, animationFrame.style.h/2)
    digit1:SetPoint("CENTER", UIParent, 0, 0)
    digit1:SetAlpha(0.0)
    -- digit1:SetTexCoord(0.25,0.5,0.33203125,0.6640625)
    -- digit1:SetPoint("CENTER", UIParent, 14,0)
    digit1.width = 128
    animationFrame.digit1 = digit1

    local digit2 = animationFrame:CreateTexture(nil, "OVERLAY")
    digit2:SetTexture(animationFrame.style.texture)
    -- digit2:SetSize(256, 256)
    digit2:SetSize(animationFrame.style.w/2, animationFrame.style.h/2)
    digit2:SetPoint("CENTER", UIParent, 0, 0)
    digit2:SetAlpha(0.0)
    -- digit2:SetTexCoord(0.25,0.5,0.0,0.33203125)
    -- digit2:SetPoint("CENTER", digit1, -46,0)
    digit2.width = 128
    animationFrame.digit2 = digit2

    local digit1Glow = animationFrame:CreateTexture(nil, "OVERLAY", nil, 2)
    digit1Glow:SetTexture(animationFrame.style.texture.."Glow")
    digit1Glow:SetPoint("TOPLEFT", digit1, 0, 0)
    digit1Glow:SetPoint("BOTTOMRIGHT", digit1, 0, 0)
    digit1Glow:SetAlpha(0.0)
    animationFrame.digit1Glow = digit1Glow
    digit1.glow = digit1Glow

    local digit2Glow = animationFrame:CreateTexture(nil, "OVERLAY", nil, 2)
    digit2Glow:SetTexture(animationFrame.style.texture.."Glow")
    digit2Glow:SetPoint("TOPLEFT", digit2, 0, 0)
    digit2Glow:SetPoint("BOTTOMRIGHT", digit2, 0, 0)
    digit2Glow:SetAlpha(0.0)
    animationFrame.digit2Glow = digit2Glow
    digit2.glow = digit2Glow

    animationFrame.digit1.width, animationFrame.digit2.width = animationFrame.style.w/2, animationFrame.style.w/2;

    Digit1AnimationGroup = digit1:CreateAnimationGroup()
    Digit2AnimationGroup = digit2:CreateAnimationGroup()
    Digit1GlowAnimationGroup = digit1Glow:CreateAnimationGroup()
    Digit2GlowAnimationGroup = digit2Glow:CreateAnimationGroup()
    animationFrame.D1AnimGroup = Digit1AnimationGroup
    animationFrame.D2AnimGroup = Digit2AnimationGroup
    animationFrame.D1GlowAnimGroup = Digit1GlowAnimationGroup
    animationFrame.D2GlowAnimGroup = Digit2GlowAnimationGroup


    local digit1ScaleDownAnimation = Digit1AnimationGroup:CreateAnimation("Scale")
    digit1ScaleDownAnimation:SetDuration(0.0)                  -- 设置动画持续时间
    digit1ScaleDownAnimation:SetScale(0.25, 0.25)              -- 设置缩放比例
    digit1ScaleDownAnimation:SetOrder(1)

    local digit2ScaleDownAnimation = Digit2AnimationGroup:CreateAnimation("Scale")
    digit2ScaleDownAnimation:SetDuration(0.0)                  -- 设置动画持续时间
    digit2ScaleDownAnimation:SetScale(0.25, 0.25)              -- 设置缩放比例
    digit2ScaleDownAnimation:SetOrder(1)

    local digit1GlowScaleDownAnimation = Digit1GlowAnimationGroup:CreateAnimation("Scale")
    digit1GlowScaleDownAnimation:SetDuration(0.0)                  -- 设置动画持续时间
    digit1GlowScaleDownAnimation:SetScale(0.25, 0.25)              -- 设置缩放比例
    digit1GlowScaleDownAnimation:SetOrder(1)

    local digit2GlowScaleDownAnimation = Digit2GlowAnimationGroup:CreateAnimation("Scale")
    digit2GlowScaleDownAnimation:SetDuration(0.0)                  -- 设置动画持续时间
    digit2GlowScaleDownAnimation:SetScale(0.25, 0.25)              -- 设置缩放比例
    digit2GlowScaleDownAnimation:SetOrder(1)

    local digit1AlphaAnimationIn = Digit1AnimationGroup:CreateAnimation("Alpha")
    digit1AlphaAnimationIn:SetDuration(0.0)     -- 设置动画持续时间
    digit1AlphaAnimationIn:SetChange(1)        -- 从当前alpha值增加1（即从0.0变为1.0）
    digit1AlphaAnimationIn:SetOrder(1)

    local digit2AlphaAnimationIn = Digit2AnimationGroup:CreateAnimation("Alpha")
    digit2AlphaAnimationIn:SetDuration(0.0)     -- 设置动画持续时间
    digit2AlphaAnimationIn:SetChange(1)        -- 从当前alpha值增加1（即从0.0变为1.0）
    digit2AlphaAnimationIn:SetOrder(1)

    local digit1GlowAlphaAnimationIn = Digit1GlowAnimationGroup:CreateAnimation("Alpha")
    digit1GlowAlphaAnimationIn:SetDuration(0.0)  -- 设置动画持续时间
    digit1GlowAlphaAnimationIn:SetChange(1)      -- 从当前alpha值增加1（即从0.0变为1.0）
    digit1GlowAlphaAnimationIn:SetOrder(1)

    local digit2GlowAlphaAnimationIn = Digit2GlowAnimationGroup:CreateAnimation("Alpha")
    digit2GlowAlphaAnimationIn:SetDuration(0.0)  -- 设置动画持续时间
    digit2GlowAlphaAnimationIn:SetChange(1)      -- 从当前alpha值增加1（即从0.0变为1.0）
    digit2GlowAlphaAnimationIn:SetOrder(1)

    local digit1ScaleUpAnimation = Digit1AnimationGroup:CreateAnimation("Scale")
    digit1ScaleUpAnimation:SetScale(4.0, 4.0)  -- 设置缩放比例
    digit1ScaleUpAnimation:SetDuration(0.3)  -- 设置动画持续时间
    digit1ScaleUpAnimation:SetSmoothing("OUT")
    digit1ScaleUpAnimation:SetOrder(2)

    local digit2ScaleUpAnimation = Digit2AnimationGroup:CreateAnimation("Scale")
    digit2ScaleUpAnimation:SetScale(4.0, 4.0)  -- 设置缩放比例
    digit2ScaleUpAnimation:SetDuration(0.3)  -- 设置动画持续时间
    digit2ScaleUpAnimation:SetSmoothing("OUT")
    digit2ScaleUpAnimation:SetOrder(2) 

    local digit1GlowScaleUpAnimation =  Digit1GlowAnimationGroup:CreateAnimation("Scale")
    digit1GlowScaleUpAnimation:SetScale(4.0, 4.0)  -- 设置缩放比例
    digit1GlowScaleUpAnimation:SetDuration(0.3)  -- 设置动画持续时间
    digit1GlowScaleUpAnimation:SetSmoothing("OUT")
    digit1GlowScaleUpAnimation:SetOrder(2)

    local digit2GlowScaleUpAnimation = Digit2GlowAnimationGroup:CreateAnimation("Scale")
    digit2GlowScaleUpAnimation:SetScale(4.0, 4.0)  -- 设置缩放比例
    digit2GlowScaleUpAnimation:SetDuration(0.3)  -- 设置动画持续时间
    digit2GlowScaleUpAnimation:SetSmoothing("OUT")
    digit2GlowScaleUpAnimation:SetOrder(2)

    local digit1GlowAlphaAnimationOut = Digit1GlowAnimationGroup:CreateAnimation("Alpha")
    digit1GlowAlphaAnimationOut:SetDuration(0.3)  -- 设置动画持续时间
    digit1GlowAlphaAnimationOut:SetChange(-1)  -- 从当前alpha值减少1.0（即从1.0变为0.0）
    digit1GlowAlphaAnimationOut:SetSmoothing("IN")
    digit1GlowAlphaAnimationOut:SetOrder(2)

    local digit2GlowAlphaAnimationOut = Digit2GlowAnimationGroup:CreateAnimation("Alpha")
    digit2GlowAlphaAnimationOut:SetDuration(0.3)  -- 设置动画持续时间
    digit2GlowAlphaAnimationOut:SetChange(-1)  -- 从当前alpha值减少1.0（即从1.0变为0.0）
    digit2GlowAlphaAnimationOut:SetSmoothing("IN")
    digit2GlowAlphaAnimationOut:SetOrder(2)

    local digit1ScaleNormalAnimation = Digit1AnimationGroup:CreateAnimation("Scale")
    digit1ScaleNormalAnimation:SetStartDelay(0.6)  -- 设置2秒延迟
    digit1ScaleNormalAnimation:SetDuration(0.1)  -- 设置动画持续时间
    digit1ScaleNormalAnimation:SetScale(1.2, 1.2)  -- 设置缩放比例
    digit1ScaleNormalAnimation:SetOrder(3)

    local digit2ScaleNormalAnimation = Digit2AnimationGroup:CreateAnimation("Scale")
    digit2ScaleNormalAnimation:SetStartDelay(0.6)  -- 设置2秒延迟
    digit2ScaleNormalAnimation:SetDuration(0.1)  -- 设置动画持续时间
    digit2ScaleNormalAnimation:SetScale(1.2, 1.2)  -- 设置缩放比例
    digit2ScaleNormalAnimation:SetOrder(3)

    local digit1AlphaAnimationOut = Digit1AnimationGroup:CreateAnimation("Alpha")
    digit1AlphaAnimationOut:SetStartDelay(0.6)  -- 设置2秒延迟
    digit1AlphaAnimationOut:SetDuration(0.1)     -- 设置动画持续时间
    digit1AlphaAnimationOut:SetChange(-1)        -- 从当前alpha值减少1.0（即从1.0变为0.0）
    digit1AlphaAnimationOut:SetOrder(3)

    local digit2AlphaAnimationOut = Digit2AnimationGroup:CreateAnimation("Alpha")
    digit2AlphaAnimationOut:SetStartDelay(0.6)  -- 设置2秒延迟
    digit2AlphaAnimationOut:SetDuration(0.1)     -- 设置动画持续时间
    digit2AlphaAnimationOut:SetChange(-1)        -- 从当前alpha值减少1.0（即从1.0变为0.0）
    digit2AlphaAnimationOut:SetOrder(3)

    Digit1AnimationGroup:SetScript("OnPlay", function()
        print("TimerAnimB: 个位数字动画开始播放")
        isAnimationRunning = true
        -- 显示动画框架
        animationFrame:Show()
        -- TimerAnimB:StartTimer_SetTexNumbers(animationFrame, animationFrame.digit1, animationFrame.digit2)
        TimerAnimB:SetOnesDigitTexture(animationFrame.time, animationFrame.digit1, animationFrame.style, animationFrame, animationFrame.digit2)
    end)

    Digit1AnimationGroup:SetScript("OnFinished", function()
        print("TimerAnimB: 个位数字动画播放完成")
        TimerAnimB:StartTimer_NumberAnimOnFinished(animationFrame)
    end)

    Digit2AnimationGroup:SetScript("OnPlay", function()
        print("TimerAnimB: 十位数字动画开始播放")
        TimerAnimB:SetTensDigitTexture(animationFrame.time, animationFrame.digit2, animationFrame.style, animationFrame, animationFrame.digit1)
    end)

    Digit2AnimationGroup:SetScript("OnFinished", function()
        print("TimerAnimB: 十位数字动画播放完成")
        TimerAnimB:StartTimer_TenNumberAnimOnFinished(animationFrame)
    end)

    Digit1GlowAnimationGroup:SetScript("OnPlay", function()
        -- print("TimerAnimB: 数字发光动画开始播放")
    end)

    Digit1GlowAnimationGroup:SetScript("OnFinished", function()
        -- print("TimerAnimB: 数字发光动画播放完成")
    end)

    Digit2GlowAnimationGroup:SetScript("OnPlay", function()
        -- print("TimerAnimB: 数字发光动画开始播放")
    end)

    Digit2GlowAnimationGroup:SetScript("OnFinished", function()
        -- print("TimerAnimB: 数字发光动画播放完成")
    end)

end

-- 创建GO纹理和发光纹理以及动画
function TimerAnimB:CreateGOAnimation()

    if not animationFrame then
        print("TimerAnimB: 错误 - 动画框架未创建")
        return
    end

    -- 创建主贴图
    local mainTexture = animationFrame:CreateTexture(nil, "OVERLAY")
    mainTexture:SetTexture(MAIN_TEXTURE)
    -- mainTexture:SetAllPoints(animationFrame)
    mainTexture:SetSize(256, 256)
    mainTexture:SetPoint("CENTER", UIParent, 0, 0)
    mainTexture:SetAlpha(0.0)
    animationFrame.mainTexture = mainTexture

    -- 创建光效贴图
    local glowTexture = animationFrame:CreateTexture(nil, "OVERLAY", nil, 2)
    glowTexture:SetTexture(GLOW_TEXTURE)
    -- glowTexture:SetAllPoints(animationFrame)
    glowTexture:SetSize(256, 256)
    glowTexture:SetPoint("CENTER", UIParent, 0, 0)
    glowTexture:SetAlpha(0.0)
    animationFrame.glowTexture = glowTexture

    -- 创建AnimationGroup - 3.3.5兼容的动画系统 贴图是可以创建动画组的
    mainTexAnimationGroup = mainTexture:CreateAnimationGroup()
    -- mainTexAnimationGroup:SetLooping("REPEAT")

    glowTexAnimationGroup = glowTexture:CreateAnimationGroup()
    -- glowTexAnimationGroup:SetLooping("REPEAT")

    local mainTexScaleDownAnimation = mainTexAnimationGroup:CreateAnimation("Scale")
    -- mainTexScaleDownAnimation:SetStartDelay(ANIMATION_DELAY)    -- 设置2秒延迟
    mainTexScaleDownAnimation:SetDuration(0.0)                  -- 设置动画持续时间
    mainTexScaleDownAnimation:SetScale(0.25, 0.25)              -- 设置缩放比例
    mainTexScaleDownAnimation:SetOrder(1)
    
    local glowTexScaleDownAnimation = glowTexAnimationGroup:CreateAnimation("Scale")
    -- glowTexScaleDownAnimation:SetStartDelay(ANIMATION_DELAY)    -- 设置2秒延迟
    glowTexScaleDownAnimation:SetDuration(0.0)                  -- 设置动画持续时间
    glowTexScaleDownAnimation:SetScale(0.25, 0.25)              -- 设置缩放比例
    glowTexScaleDownAnimation:SetOrder(1)

    -- 创建Alpha动画 - 控制透明度渐变 
    local mainTexalphaAnimationIn = mainTexAnimationGroup:CreateAnimation("Alpha") -- 如果持续时间少了 那么透明度又会回到0.0 那么还是会只会看见光效而不见贴图
    mainTexalphaAnimationIn:SetDuration(0.0)     -- 设置动画持续时间
    mainTexalphaAnimationIn:SetChange(1)        -- 从当前alpha值增加1（即从0.0变为1.0）
    mainTexalphaAnimationIn:SetOrder(2)

    local glowTexalphaAnimationIn = glowTexAnimationGroup:CreateAnimation("Alpha")
    glowTexalphaAnimationIn:SetDuration(0.0)  -- 设置动画持续时间
    glowTexalphaAnimationIn:SetChange(1)      -- 从当前alpha值增加1（即从0.0变为1.0）
    glowTexalphaAnimationIn:SetOrder(2)

    local mainTexScaleUpAnimation = mainTexAnimationGroup:CreateAnimation("Scale")
    -- mainTexScaleUpAnimation:SetStartDelay(ANIMATION_DELAY + ANIMATION_DURATION)  -- 设置2秒延迟
    mainTexScaleUpAnimation:SetScale(4.0, 4.0)  -- 设置缩放比例
    mainTexScaleUpAnimation:SetDuration(0.4)  -- 设置动画持续时间
    mainTexScaleUpAnimation:SetSmoothing("OUT")
    mainTexScaleUpAnimation:SetOrder(3)

    local glowTexScaleUpAnimation = glowTexAnimationGroup:CreateAnimation("Scale")
    -- glowTexScaleUpAnimation:SetStartDelay(ANIMATION_DELAY + ANIMATION_DURATION)  -- 设置2秒延迟
    glowTexScaleUpAnimation:SetScale(4.0, 4.0)  -- 设置缩放比例
    glowTexScaleUpAnimation:SetDuration(0.4)  -- 设置动画持续时间
    glowTexScaleUpAnimation:SetSmoothing("OUT")
    glowTexScaleUpAnimation:SetOrder(3)

    local glowTexalphaAnimationOut = glowTexAnimationGroup:CreateAnimation("Alpha")
    -- glowTexalphaAnimationOut:SetStartDelay(ANIMATION_DELAY + ANIMATION_DURATION)  -- 设置2秒延迟
    glowTexalphaAnimationOut:SetDuration(0.4)  -- 设置动画持续时间
    glowTexalphaAnimationOut:SetChange(-1)  -- 从当前alpha值减少1.0（即从1.0变为0.0）
    glowTexalphaAnimationOut:SetSmoothing("IN")
    glowTexalphaAnimationOut:SetOrder(3)

    local mainTexScaleNormalAnimation = mainTexAnimationGroup:CreateAnimation("Scale")
    mainTexScaleNormalAnimation:SetStartDelay(0.6)  -- 设置2秒延迟
    mainTexScaleNormalAnimation:SetDuration(0.2)  -- 设置动画持续时间
    mainTexScaleNormalAnimation:SetScale(1.4, 1.4)  -- 设置缩放比例
    mainTexScaleNormalAnimation:SetSmoothing("OUT")
    mainTexScaleNormalAnimation:SetOrder(4)

    local mainTexalphaAnimationOut = mainTexAnimationGroup:CreateAnimation("Alpha") -- 如果持续时间少了 那么透明度又会回到0.0 那么还是会只会看见光效而不见贴图
    mainTexalphaAnimationOut:SetStartDelay(0.6)  -- 设置2秒延迟
    mainTexalphaAnimationOut:SetDuration(0.2)     -- 设置动画持续时间
    mainTexalphaAnimationOut:SetChange(-1)        -- 从当前alpha值减少1.0（即从1.0变为0.0）
    mainTexalphaAnimationOut:SetSmoothing("OUT")
    mainTexalphaAnimationOut:SetOrder(4)

    -- 设置动画组的事件处理
    mainTexAnimationGroup:SetScript("OnPlay", function()
        print("TimerAnimB: GO动画开始播放")
        isAnimationRunning = true
        -- 显示动画框架
        animationFrame:Show()
    end)

    mainTexAnimationGroup:SetScript("OnFinished", function()
        print("TimerAnimB: GO动画播放完成")
        TimerAnimB:OnAnimationFinished()
    end)

    mainTexAnimationGroup:SetScript("OnStop", function()
        print("TimerAnimB: GO动画被停止")
        TimerAnimB:OnAnimationFinished()
    end)

end

-- 开始动画播放
function TimerAnimB:StartAnimation()
    if isAnimationRunning then
        print("TimerAnimB: 动画已在运行中，跳过重复启动")
        return  -- 防止重复启动动画
    end

    if not mainTexAnimationGroup then
        print("TimerAnimB: 错误 - AnimationGroup未创建")
        return
    end

    if not glowTexAnimationGroup then
        print("TimerAnimB: 错误 - AnimationGroup2未创建")
        return
    end

    if not animationFrame then
        print("TimerAnimB: 错误 - 动画框架未创建")
        return
    end

    if animationFrame.time <= 0 then
        animationFrame.time = 20
        animationFrame.tentime = 20
    end

    print("TimerAnimB: 启动光效渐变动画（延迟" .. ANIMATION_DELAY .. "秒）")

    -- 播放动画组
    -- mainTexAnimationGroup:Play()
    -- glowTexAnimationGroup:Play()
    TimerAnimB:NumberAnimationPlay()
end

function TimerAnimB:NumberAnimationPlay()

    if not Digit1AnimationGroup then
        print("TimerAnimB: 错误 - AnimationGroup未创建")
        return
    end

    if not Digit2AnimationGroup then
        print("TimerAnimB: 错误 - AnimationGroup未创建")
        return
    end

    if not Digit1GlowAnimationGroup then
        print("TimerAnimB: 错误 - AnimationGroup未创建")
        return
    end

    if not Digit2GlowAnimationGroup then
        print("TimerAnimB: 错误 - AnimationGroup未创建")
        return
    end
    print("TimerAnimB: 开始播放数字动画...")
    Digit1AnimationGroup:Play()
    Digit2AnimationGroup:Play()
    Digit1GlowAnimationGroup:Play()
    Digit2GlowAnimationGroup:Play()
end

function TimerAnimB:GOAnimationPlay()
    if not mainTexAnimationGroup then
        print("TimerAnimB: 错误 - AnimationGroup未创建")
        return
    end

    if not glowTexAnimationGroup then
        print("TimerAnimB: 错误 - AnimationGroup2未创建")
        return
    end

    mainTexAnimationGroup:Play()
    glowTexAnimationGroup:Play()

end

-- 动画完成处理
function TimerAnimB:OnAnimationFinished()
    isAnimationRunning = false

    -- 隐藏动画框架
    if animationFrame then
        animationFrame:Hide()
    end

    print("TimerAnimB: 动画完成，资源已清理")
end

-- 停止动画
function TimerAnimB:StopAnimation()
    if mainTexAnimationGroup and isAnimationRunning then
        mainTexAnimationGroup:Stop()
        print("TimerAnimB: 手动停止动画")
    else
        print("TimerAnimB: 没有正在运行的动画")
    end
end

-- 数字动画播放完成
function TimerAnimB:StartTimer_NumberAnimOnFinished(self)
    -- 每次动画完成代表1秒过去
	self.time = self.time - 1;

    if self.time > 0 then

        self.D1AnimGroup:Play();
        self.D1GlowAnimGroup:Play();

	else
        TimerAnimB:GOAnimationPlay()
	end
    
end

function TimerAnimB:StartTimer_TenNumberAnimOnFinished(self)
    self.tentime = self.tentime - 1;
    if self.tentime >= 10 then
        self.D2AnimGroup:Play();
        self.D2GlowAnimGroup:Play();
    end
end

-- 玩家登录事件处理
function TimerAnimB:OnPlayerLogin()
    print("TimerAnimB: 检测到玩家登录事件")

    -- 确保动画框架已创建
    if not animationFrame then
        TimerAnimB:CreateAnimationFrame()
    end

    -- 启动动画
    TimerAnimB:StartAnimation()
end

-- 插件加载事件处理
function TimerAnimB:OnAddonLoaded(addonName)
    if addonName == "TimerAnimB" then
        print("TimerAnimB: 插件加载完成")

        -- 创建动画框架
        TimerAnimB:CreateAnimationFrame()
    end
end

--[[
	个位数纹理显示函数

	功能说明：
		计算并设置个位数的纹理坐标和显示属性
		包含完整的纹理坐标计算、半宽度设置和位置布局逻辑
		自动计算总偏移量用于居中显示

	参数说明：
		timeValue: 需要显示的完整数字值
		digitTexture: 个位数纹理对象
		style: 数字样式配置
		parentFrame: 父框架对象（用于位置设置，可选）
		tensDigitTexture: 十位数纹理对象（用于获取宽度属性，可选）

	返回值：
		digit: 个位数值 (0-9)
		halfWidth: 个位数的半宽度（用于布局计算）

	实现细节：
		- 使用取模运算提取个位数
		- 计算纹理图集中的坐标位置
		- 设置纹理坐标和发光效果
		- 计算并设置半宽度属性用于布局
		- 自动计算总偏移量并设置位置（个位数作为基准位置）
--]]
function TimerAnimB:SetOnesDigitTexture(timeValue, digitTexture, style, parentFrame, tensDigitTexture)
	-- 提取个位数
	local digit = mod(timeValue, 10);

	-- 计算纹理坐标转换比例
	local texCoW = style.w/style.texW;		-- 单个数字宽度占总纹理宽度的比例
	local texCoH = style.h/style.texH;		-- 单个数字高度占总纹理高度的比例
	local columns = floor(style.texW/style.w);	-- 纹理图集的列数

	-- 计算数字在纹理图集中的位置
	local l = mod(digit, columns) * texCoW;		-- 左边界：列位置 * 单列宽度
	local r = l + texCoW;						-- 右边界：左边界 + 单列宽度
	local t = floor(digit/columns) * texCoH;	-- 上边界：行位置 * 单行高度
	local b = t + texCoH;						-- 下边界：上边界 + 单行高度

	-- 设置数字纹理和发光效果的纹理坐标
	digitTexture:SetTexCoord(l,r,t,b);
	parentFrame.digit1Glow:SetTexCoord(l,r,t,b);

	-- 计算当前数字的半宽度（用于精确定位）
	local halfWidth = style.numberHalfWidths[digit+1] * digitTexture.width;
	digitTexture.hw = halfWidth;

	-- 自动计算总偏移量并设置个位数的位置
	if parentFrame then
		-- 计算总偏移量：个位数半宽度 + 十位数半宽度（如果存在）
		local totalOffset = halfWidth;

		-- 计算十位数值，如果存在则计算其半宽度
		local tensValue = floor(timeValue / 10);
		if tensValue > 0 then
			local tensDigit = mod(tensValue, 10);
			local tensHalfWidth = style.numberHalfWidths[tensDigit+1] * (tensDigitTexture and tensDigitTexture.width or digitTexture.width);
			totalOffset = totalOffset + tensHalfWidth;
		end

		digitTexture:ClearAllPoints();
        
        -- 普通模式：相对于父框架居中
		digitTexture:SetPoint("CENTER", UIParent, totalOffset - halfWidth, 0);
        print("个位数 = "..digit.. "| 个位数坐标 ： "..(totalOffset - halfWidth))
	end

	return digit, halfWidth;
end

--[[
	十位数纹理显示函数

	功能说明：
		计算并设置十位数的纹理坐标和显示属性
		包含完整的纹理坐标计算、半宽度设置和位置布局逻辑
		处理没有十位数时的隐藏逻辑

	参数说明：
		timeValue: 需要显示的完整数字值
		digitTexture: 十位数纹理对象
		style: 数字样式配置
		onesDigitTexture: 个位数纹理对象（用于相对定位）

	返回值：
		digit: 十位数值 (0-9)，如果没有十位数则返回nil
		halfWidth: 十位数的半宽度（用于布局计算），如果没有十位数则返回0

	实现细节：
		- 通过除法和取模运算提取十位数
		- 当数值小于10时隐藏十位数纹理
		- 计算纹理图集中的坐标位置
		- 设置纹理坐标和发光效果
		- 计算并设置半宽度属性用于布局
		- 设置十位数相对于个位数的位置（从右到左排列）
--]]
function TimerAnimB:SetTensDigitTexture(timeValue, digitTexture, style, parentFrame, onesDigitTexture)
	-- 计算十位数值
	local tensValue = floor(timeValue / 10);

	-- 如果没有十位数，隐藏纹理
	if tensValue <= 0 then
		digitTexture:SetTexCoord(0,0,0,0);
		digitTexture.glow:SetTexCoord(0,0,0,0);
		digitTexture.hw = 0;  -- 设置半宽度为0
		return nil, 0;
	end

	-- 提取十位数
	local digit = mod(tensValue, 10);

	-- 计算纹理坐标转换比例
	local texCoW = style.w/style.texW;		-- 单个数字宽度占总纹理宽度的比例
	local texCoH = style.h/style.texH;		-- 单个数字高度占总纹理高度的比例
	local columns = floor(style.texW/style.w);	-- 纹理图集的列数

	-- 计算数字在纹理图集中的位置
	local l = mod(digit, columns) * texCoW;		-- 左边界：列位置 * 单列宽度
	local r = l + texCoW;						-- 右边界：左边界 + 单列宽度
	local t = floor(digit/columns) * texCoH;	-- 上边界：行位置 * 单行高度
	local b = t + texCoH;						-- 下边界：上边界 + 单行高度

	-- 设置数字纹理和发光效果的纹理坐标
	digitTexture:SetTexCoord(l,r,t,b);
	parentFrame.digit2Glow:SetTexCoord(l,r,t,b);

	-- 计算当前数字的半宽度（用于精确定位）
	local halfWidth = style.numberHalfWidths[digit+1] * digitTexture.width;
	digitTexture.hw = halfWidth;

	-- 设置十位数的位置（相对于个位数向左偏移）
	if onesDigitTexture then
		-- 自动计算个位数的半宽度
		local onesDigit = mod(timeValue, 10);
		local onesHalfWidth = style.numberHalfWidths[onesDigit+1] * (onesDigitTexture.width or digitTexture.width);

		digitTexture:ClearAllPoints();
		-- 十位数相对于个位数向左偏移，实现从右到左的排列
		digitTexture:SetPoint("CENTER", onesDigitTexture, -(halfWidth + onesHalfWidth), 0);
        print("十位数 = "..digit.. "| 十位数坐标 ： "..-(halfWidth + onesHalfWidth))
	end

	return digit, halfWidth;
end

function TimerAnimB:StartTimer_SetTexNumbers(self, ...)
	local digits = {...}
	local timeDigits = floor(self.time);
	local digit;
	local style = self.style;
	local i = 1;
	
	local texCoW = style.w/style.texW;
	local texCoH = style.h/style.texH;
	local l,r,t,b;
	local columns = floor(style.texW/style.w);
	local numberOffset = 0;
	local numShown = 0;

	while digits[i] do -- THIS WILL DISPLAY SECOND AS A NUMBER 2:34 would be 154
		if timeDigits > 0 then
			digit = mod(timeDigits, 10);
			
			digits[i].hw = style.numberHalfWidths[digit+1]*digits[i].width;
			numberOffset  = numberOffset + digits[i].hw;
			
			l = mod(digit, columns) * texCoW;
			r = l + texCoW;
			t = floor(digit/columns) * texCoH;
			b = t + texCoH;

			digits[i]:SetTexCoord(l,r,t,b);
			digits[i].glow:SetTexCoord(l,r,t,b);
			
			timeDigits = floor(timeDigits/10);	
			numShown = numShown + 1;			
		else
			digits[i]:SetTexCoord(0,0,0,0);
			digits[i].glow:SetTexCoord(0,0,0,0);
		end
		i = i + 1;
	end
	
	if numberOffset > 0 then
		digits[1]:ClearAllPoints();
		digits[1]:SetPoint("CENTER", self, "CENTER", numberOffset - digits[1].hw, 0);

		for i=2,numShown do
			digits[i]:ClearAllPoints();
			digits[i]:SetPoint("CENTER", digits[i-1], "CENTER", -(digits[i].hw + digits[i-1].hw), 0)
			i = i + 1;
		end
	end
end

-- 事件处理函数
local function OnEvent(_, event, ...)
    if event == "ADDON_LOADED" then
        TimerAnimB:OnAddonLoaded(...)
    elseif event == "PLAYER_LOGIN" then
        TimerAnimB:OnPlayerLogin()
    end
end

-- 创建事件监听框架
local eventFrame = CreateFrame("Frame")
eventFrame:RegisterEvent("ADDON_LOADED")
eventFrame:RegisterEvent("PLAYER_LOGIN")
eventFrame:SetScript("OnEvent", OnEvent)

-- 斜杠命令支持（用于测试和调试）
SLASH_TIMERANIMB1 = "/timeranimb"
SLASH_TIMERANIMB2 = "/tab"
SlashCmdList["TIMERANIMB"] = function(msg)
    local command = string.lower(msg or "")

    if command == "test" then
        print("TimerAnimB: 执行测试动画")
        if not animationFrame then
            TimerAnimB:CreateAnimationFrame()
        end
        TimerAnimB:StartAnimation()
    elseif command == "stop" then
        print("TimerAnimB: 停止动画")
        TimerAnimB:StopAnimation()
    elseif command == "status" then
        print("TimerAnimB: 动画状态 - " .. (isAnimationRunning and "运行中" or "已停止"))
        print("TimerAnimB: 框架状态 - " .. (animationFrame and "已创建" or "未创建"))
        print("TimerAnimB: 动画组状态 - " .. (mainTexAnimationGroup and "已创建" or "未创建"))
    else
        print("TimerAnimB 命令帮助:")
        print("/timeranimb test - 测试动画效果")
        print("/timeranimb stop - 停止当前动画")
        print("/timeranimb status - 查看插件状态")
    end
end

print("TimerAnimB: 插件初始化完成 - 使用AnimationGroup和Alpha动画系统")