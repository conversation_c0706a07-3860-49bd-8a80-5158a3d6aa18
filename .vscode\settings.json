{"Lua.runtime.version": "Lua 5.1", "Lua.runtime.builtin": {"basic": "disable", "debug": "disable", "io": "disable", "math": "disable", "os": "disable", "package": "disable", "string": "disable", "table": "disable", "utf8": "disable"}, "Lua.workspace.library": ["~\\.vscode\\extensions\\ketho.wow-api-0.20.8\\Annotations\\Core"], "Lua.diagnostics.globals": ["SlashCmdList", "FreeTimerTrackerTimer", "StartTimer_SwitchToLargeDisplay", "SOUNDKIT", "TimerTracker", "StartTimer_SetTexNumbers"]}