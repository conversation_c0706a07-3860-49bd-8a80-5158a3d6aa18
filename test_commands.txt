# TimerAnimB 测试命令指南

## 基本测试流程

### 1. 插件加载测试
进入游戏后，在聊天窗口查看是否有以下信息：
```
TimerAnimB: 插件初始化完成 - 使用AnimationGroup和Alpha动画系统
TimerAnimB: 插件加载完成
TimerAnimB: 动画框架和AnimationGroup创建完成
```

### 2. 登录动画测试
重新登录角色，应该看到：
```
TimerAnimB: 检测到玩家登录事件
TimerAnimB: 启动光效渐变动画（延迟2.0秒）
TimerAnimB: 动画开始播放
TimerAnimB: 动画播放完成
TimerAnimB: 动画完成，资源已清理
```

### 3. 手动测试命令

#### 播放测试动画
```
/timeranimb test
```
或
```
/tab test
```

预期输出：
```
TimerAnimB: 执行测试动画
TimerAnimB: 启动光效渐变动画（延迟2.0秒）
TimerAnimB: 动画开始播放
```

#### 停止动画
```
/timeranimb stop
```
或
```
/tab stop
```

预期输出：
```
TimerAnimB: 手动停止动画
TimerAnimB: 动画被停止
TimerAnimB: 动画完成，资源已清理
```

#### 查看状态
```
/timeranimb status
```
或
```
/tab status
```

预期输出：
```
TimerAnimB: 动画状态 - 已停止
TimerAnimB: 框架状态 - 已创建
TimerAnimB: 动画组状态 - 已创建
```

#### 帮助信息
```
/timeranimb
```
或
```
/tab
```

预期输出：
```
TimerAnimB 命令帮助:
/timeranimb test - 测试动画效果
/timeranimb stop - 停止当前动画
/timeranimb status - 查看插件状态
```

## 动画效果验证

### 视觉效果检查点
1. **动画开始前**：屏幕中央应该没有任何显示
2. **延迟2秒后**：屏幕中央出现两个贴图（主贴图和光效贴图）
3. **动画过程中**：贴图从完全不透明(alpha=1.0)逐渐变为完全透明(alpha=0.0)
4. **动画完成后**：贴图完全消失，屏幕恢复正常

### 时序验证
- **总时长**：4.5秒（2秒延迟 + 2.5秒动画）
- **延迟阶段**：前2秒无任何显示
- **动画阶段**：2.5秒的平滑渐变效果

## 故障排除测试

### 重复启动保护测试
1. 执行 `/tab test`
2. 在动画播放过程中再次执行 `/tab test`
3. 应该看到：`TimerAnimB: 动画已在运行中，跳过重复启动`

### 错误处理测试
如果出现任何错误，插件应该输出相应的错误信息，例如：
- `TimerAnimB: 错误 - AnimationGroup未创建`
- `TimerAnimB: 没有正在运行的动画`

### 资源清理验证
1. 播放动画
2. 等待动画自然完成
3. 检查是否有 `TimerAnimB: 动画完成，资源已清理` 消息
4. 使用 `/tab status` 确认状态为"已停止"

## 性能测试

### 内存使用
- 插件应该不会造成明显的内存泄漏
- 动画完成后内存使用应该恢复到初始水平

### 帧率影响
- 动画播放期间不应该有明显的帧率下降
- 使用官方AnimationGroup API确保性能优化

## 兼容性测试

### 与其他插件的兼容性
- 确保不会与其他UI插件产生冲突
- 动画显示在最高层级，不应该被其他UI元素遮挡

### 不同分辨率测试
- 在不同分辨率下测试动画位置是否居中
- 确保贴图显示正常，无拉伸或变形

---

**按照以上步骤进行测试，确保插件功能完全正常！**
